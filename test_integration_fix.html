<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gmail Integration Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-gray-900 text-white p-8">
    <div class="max-w-md mx-auto">
        <h1 class="text-2xl font-bold mb-6">Gmail Integration Test</h1>
        
        <!-- Gmail Integration Card -->
        <div class="integration-card p-6 bg-gray-800 border border-gray-700 rounded-lg">
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center space-x-3">
                    <div class="w-12 h-12 rounded-lg bg-red-500 flex items-center justify-center">
                        <i class="fas fa-envelope text-white text-lg"></i>
                    </div>
                    <div>
                        <h4 class="text-white font-semibold">Gmail</h4>
                        <p class="text-xs text-gray-400">Email automation & support</p>
                    </div>
                </div>
                <div class="integration-status" data-integration="gmail">
                    <span class="status-badge bg-gray-600 text-gray-300 px-3 py-1 rounded-full text-xs font-medium">Not Connected</span>
                </div>
            </div>
            <p class="text-sm text-gray-400 mb-6 leading-relaxed">Connect your Gmail account to enable email automation, support ticket management, and seamless communication workflows with your AI agents.</p>
            <div class="integration-buttons">
                <button class="connect-btn w-full px-4 py-3 bg-blue-500 hover:bg-blue-600 text-white font-semibold rounded-md transition-colors text-sm" data-integration="gmail">
                    <i class="fas fa-plug mr-2"></i>Connect Gmail
                </button>
                <button class="disconnect-btn w-full px-4 py-3 bg-red-600 hover:bg-red-700 text-white font-semibold rounded-md transition-colors text-sm hidden" data-integration="gmail">
                    <i class="fas fa-unlink mr-2"></i>Disconnect Gmail
                </button>
            </div>
        </div>
        
        <div class="mt-6 p-4 bg-gray-800 rounded-lg">
            <h3 class="font-semibold mb-2">Test Instructions:</h3>
            <ol class="text-sm text-gray-300 space-y-1">
                <li>1. Click "Connect Gmail" - should show "Connecting..." state</li>
                <li>2. After 2 seconds, should show "Connected" with disconnect button</li>
                <li>3. Click "Disconnect Gmail" - should show "Disconnecting..." state</li>
                <li>4. After 2 seconds, should return to "Not Connected" state</li>
                <li>5. Repeat multiple times to test button responsiveness</li>
            </ol>
        </div>
    </div>

    <script>
        // Simplified test version of the integration functions
        function getIntegrationDisplayName(integration) {
            const names = { 'gmail': 'Gmail' };
            return names[integration] || integration.charAt(0).toUpperCase() + integration.slice(1);
        }

        function setIntegrationConnecting(integration, isConnecting) {
            const element = document.querySelector(`[data-integration="${integration}"]`);
            if (!element) return;

            const card = element.closest('.integration-card');
            if (!card) return;

            const button = card.querySelector('.connect-btn');
            const disconnectBtn = card.querySelector('.disconnect-btn');
            const statusBadge = card.querySelector('.status-badge');

            if (!button || !statusBadge) return;

            if (isConnecting) {
                statusBadge.className = 'status-badge connecting px-2 py-1 rounded-full text-xs bg-yellow-600 text-white';
                statusBadge.textContent = 'Connecting...';
                button.classList.add('connecting');
                button.classList.remove('hidden');
                button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Connecting...';
                button.disabled = true;
                
                if (disconnectBtn) {
                    disconnectBtn.classList.add('hidden');
                    disconnectBtn.disabled = true;
                }
            } else {
                statusBadge.className = 'status-badge bg-gray-600 text-gray-300 px-2 py-1 rounded-full text-xs';
                statusBadge.textContent = 'Not Connected';
                button.classList.remove('connecting', 'hidden');
                const displayName = getIntegrationDisplayName(integration);
                button.innerHTML = `<i class="fas fa-plug mr-2"></i>Connect ${displayName}`;
                button.disabled = false;
                
                if (disconnectBtn) {
                    disconnectBtn.classList.add('hidden');
                    disconnectBtn.disabled = true;
                }
            }
        }

        function setIntegrationDisconnecting(integration, isDisconnecting) {
            const element = document.querySelector(`[data-integration="${integration}"]`);
            if (!element) return;

            const card = element.closest('.integration-card');
            if (!card) return;

            const connectBtn = card.querySelector('.connect-btn');
            const disconnectBtn = card.querySelector('.disconnect-btn');
            const statusBadge = card.querySelector('.status-badge');

            if (!connectBtn || !disconnectBtn || !statusBadge) return;

            if (isDisconnecting) {
                statusBadge.className = 'status-badge disconnecting px-2 py-1 rounded-full text-xs bg-red-600 text-white';
                statusBadge.textContent = 'Disconnecting...';
                connectBtn.classList.add('hidden');
                connectBtn.disabled = true;
                
                disconnectBtn.classList.remove('hidden');
                disconnectBtn.classList.add('disconnecting');
                disconnectBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Disconnecting...';
                disconnectBtn.disabled = true;
            } else {
                statusBadge.className = 'status-badge connected px-2 py-1 rounded-full text-xs bg-green-600 text-white';
                statusBadge.textContent = 'Connected';
                connectBtn.classList.add('connected', 'hidden');
                connectBtn.disabled = true;
                
                disconnectBtn.classList.remove('hidden', 'disconnecting');
                const displayName = getIntegrationDisplayName(integration);
                disconnectBtn.innerHTML = `<i class="fas fa-unlink mr-2"></i>Disconnect ${displayName}`;
                disconnectBtn.disabled = false;
            }
        }

        async function handleIntegrationConnect(event) {
            const button = event.target.closest('.connect-btn');
            if (!button) return;
            
            const integration = button.getAttribute('data-integration');
            if (!integration) return;

            console.log('Integration connect clicked:', integration);

            if (button.classList.contains('connected') || button.classList.contains('connecting') || button.disabled) {
                console.log('Button is already connected, connecting, or disabled. Ignoring click.');
                return;
            }

            setIntegrationConnecting(integration, true);

            try {
                // Simulate connection process
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                // Update to connected state
                setIntegrationDisconnecting(integration, false); // This sets to connected state
                console.log('Gmail connected successfully!');

            } catch (error) {
                console.error('Integration connection error:', error);
                setIntegrationConnecting(integration, false);
            }
        }

        async function handleIntegrationDisconnect(event) {
            const button = event.target.closest('.disconnect-btn');
            if (!button) return;

            const integration = button.getAttribute('data-integration');
            if (!integration) return;

            if (button.disabled || button.classList.contains('disconnecting')) {
                console.log('Disconnect already in progress, ignoring click');
                return;
            }

            console.log('Integration disconnect clicked:', integration);

            setIntegrationDisconnecting(integration, true);

            try {
                // Simulate disconnection process
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                // Update to disconnected state
                setIntegrationConnecting(integration, false); // This sets to disconnected state
                console.log('Gmail disconnected successfully!');

            } catch (error) {
                console.error('Integration disconnection error:', error);
                setIntegrationDisconnecting(integration, false);
            }
        }

        // Setup event listeners
        document.addEventListener('DOMContentLoaded', function() {
            const connectBtn = document.querySelector('.connect-btn');
            if (connectBtn) {
                connectBtn.addEventListener('click', handleIntegrationConnect);
            }

            document.addEventListener('click', function(event) {
                if (event.target.closest('.disconnect-btn')) {
                    handleIntegrationDisconnect(event);
                }
            });

            console.log('Test page loaded - ready to test Gmail integration buttons');
        });
    </script>
</body>
</html>
