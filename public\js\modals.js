// Modal Logic and Interactions

// Delete Agent Modal Functions
function showDeleteAgentModal(agentId, agentName) {
    const modal = document.getElementById('deleteAgentModal');
    const agentNameSpan = document.getElementById('deleteAgentName');
    const confirmBtn = document.getElementById('confirmDeleteAgent');
    
    if (!modal || !agentNameSpan || !confirmBtn) {
        console.error('Delete agent modal elements not found');
        return;
    }
    
    // Set agent name in modal
    agentNameSpan.textContent = agentName;
    
    // Store agent ID for deletion
    confirmBtn.setAttribute('data-agent-id', agentId);
    
    // Show modal
    modal.style.display = 'flex';
    
    // Add fade-in animation
    setTimeout(() => {
        modal.classList.add('modal-visible');
    }, 10);
    
    // Prevent body scroll
    document.body.style.overflow = 'hidden';
}

function hideDeleteAgentModal() {
    const modal = document.getElementById('deleteAgentModal');
    if (!modal) return;
    
    // Add fade-out animation
    modal.classList.remove('modal-visible');
    
    // Hide modal after animation
    setTimeout(() => {
        modal.style.display = 'none';
        
        // Restore body scroll
        document.body.style.overflow = '';
    }, 300);
}

// Profile Edit Modal Functions
function showProfileEditModal() {
    const modal = document.getElementById('profileEditModal');
    if (!modal) {
        console.error('Profile edit modal not found');
        return;
    }
    
    // Pre-populate form with current user data
    populateProfileForm();
    
    // Show modal
    modal.style.display = 'flex';
    
    // Add fade-in animation
    setTimeout(() => {
        modal.classList.add('modal-visible');
    }, 10);
    
    // Prevent body scroll
    document.body.style.overflow = 'hidden';
}

function hideProfileEditModal() {
    const modal = document.getElementById('profileEditModal');
    if (!modal) return;
    
    // Add fade-out animation
    modal.classList.remove('modal-visible');
    
    // Hide modal after animation
    setTimeout(() => {
        modal.style.display = 'none';
        
        // Restore body scroll
        document.body.style.overflow = '';
    }, 300);
}

function populateProfileForm() {
    // Get current user data from global variables or localStorage
    const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}');
    
    // Populate form fields
    const firstNameInput = document.getElementById('editFirstName');
    const lastNameInput = document.getElementById('editLastName');
    const emailInput = document.getElementById('editEmail');
    
    if (firstNameInput && currentUser.first_name) {
        firstNameInput.value = currentUser.first_name;
    }
    if (lastNameInput && currentUser.last_name) {
        lastNameInput.value = currentUser.last_name;
    }
    if (emailInput && currentUser.email) {
        emailInput.value = currentUser.email;
    }
}

async function handleProfileEditSubmit(event) {
    event.preventDefault();
    
    const form = event.target;
    const formData = new FormData(form);
    
    const firstName = formData.get('firstName');
    const lastName = formData.get('lastName');
    const email = formData.get('email');
    
    try {
        // Show loading state
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;
        submitBtn.textContent = 'Updating...';
        submitBtn.disabled = true;
        
        // Update user profile via API
        const response = await updateUserProfile({
            first_name: firstName,
            last_name: lastName,
            email: email
        });
        
        if (response.success) {
            // Update local storage
            const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}');
            currentUser.first_name = firstName;
            currentUser.last_name = lastName;
            currentUser.email = email;
            localStorage.setItem('currentUser', JSON.stringify(currentUser));
            
            // Update UI
            updateProfileDisplay();
            
            // Show success message
            showToast('Profile updated successfully!', 'success');
            
            // Hide modal
            hideProfileEditModal();
        } else {
            throw new Error(response.error || 'Failed to update profile');
        }
    } catch (error) {
        console.error('Error updating profile:', error);
        showToast('Failed to update profile. Please try again.', 'error');
    } finally {
        // Reset button state
        const submitBtn = form.querySelector('button[type="submit"]');
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    }
}

// FAQ Modal Functions
function openFAQModal(faqId) {
    const modal = document.getElementById('faqModal');
    const modalTitle = document.getElementById('faqModalTitle');
    const modalContent = document.getElementById('faqModalContent');
    
    if (!modal || !modalTitle || !modalContent) {
        console.error('FAQ modal elements not found');
        return;
    }
    
    // FAQ content data
    const faqData = {
        'faq1': {
            title: 'What is Veritas Agent?',
            content: `
                <p>Veritas Agent is an advanced AI automation platform designed to streamline business operations through intelligent automation solutions. Our platform integrates seamlessly with your existing software ecosystem to create custom AI agents that handle repetitive tasks, improve efficiency, and drive measurable ROI.</p>
                
                <h4>Key Features:</h4>
                <ul>
                    <li><strong>Custom AI Agent Development:</strong> Tailored automation solutions for your specific business needs</li>
                    <li><strong>Seamless Integration:</strong> Works with your existing CRM, project management, and communication tools</li>
                    <li><strong>Real-time Analytics:</strong> Track performance and ROI with detailed reporting</li>
                    <li><strong>Scalable Architecture:</strong> Grows with your business requirements</li>
                </ul>
                
                <p>Our platform is designed for businesses of all sizes looking to reduce manual workload, eliminate errors, and focus on strategic growth initiatives.</p>
            `
        },
        'faq2': {
            title: 'Implementation Timeline',
            content: `
                <p>The implementation timeline for Veritas Agent varies based on your specific requirements and the complexity of your existing systems. Here's our typical process:</p>
                
                <h4>Phase 1: Discovery & Planning (1-2 weeks)</h4>
                <ul>
                    <li>Initial consultation and requirements gathering</li>
                    <li>System analysis and integration planning</li>
                    <li>Custom solution design and approval</li>
                </ul>
                
                <h4>Phase 2: Development & Testing (2-4 weeks)</h4>
                <ul>
                    <li>Custom AI agent development</li>
                    <li>Integration with your existing systems</li>
                    <li>Comprehensive testing and optimization</li>
                </ul>
                
                <h4>Phase 3: Deployment & Training (1 week)</h4>
                <ul>
                    <li>Production deployment</li>
                    <li>Team training and documentation</li>
                    <li>Go-live support and monitoring</li>
                </ul>
                
                <p><strong>Total Timeline:</strong> Most implementations are completed within 4-7 weeks from initial consultation to full deployment.</p>
            `
        }
        // Add more FAQ items as needed
    };
    
    const faq = faqData[faqId];
    if (!faq) {
        console.error('FAQ content not found for ID:', faqId);
        return;
    }
    
    // Set modal content
    modalTitle.textContent = faq.title;
    modalContent.innerHTML = faq.content;
    
    // Show modal
    modal.style.display = 'flex';
    
    // Add fade-in animation
    setTimeout(() => {
        modal.classList.add('modal-visible');
    }, 10);
    
    // Prevent body scroll
    document.body.style.overflow = 'hidden';
}

function closeFAQModal() {
    const modal = document.getElementById('faqModal');
    if (!modal) return;
    
    // Add fade-out animation
    modal.classList.remove('modal-visible');
    
    // Hide modal after animation
    setTimeout(() => {
        modal.style.display = 'none';
        
        // Restore body scroll
        document.body.style.overflow = '';
    }, 300);
}

// Setup Modal Event Listeners
function setupModalEventListeners() {
    // Delete Agent Modal
    const deleteAgentModal = document.getElementById('deleteAgentModal');
    if (deleteAgentModal) {
        // Close button
        const closeBtn = deleteAgentModal.querySelector('.modal-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', hideDeleteAgentModal);
        }
        
        // Cancel button
        const cancelBtn = deleteAgentModal.querySelector('#cancelDeleteAgent');
        if (cancelBtn) {
            cancelBtn.addEventListener('click', hideDeleteAgentModal);
        }
        
        // Click outside to close
        deleteAgentModal.addEventListener('click', (event) => {
            if (event.target === deleteAgentModal) {
                hideDeleteAgentModal();
            }
        });
    }
    
    // Profile Edit Modal
    const profileEditModal = document.getElementById('profileEditModal');
    if (profileEditModal) {
        // Close button
        const closeBtn = profileEditModal.querySelector('.modal-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', hideProfileEditModal);
        }
        
        // Click outside to close
        profileEditModal.addEventListener('click', (event) => {
            if (event.target === profileEditModal) {
                hideProfileEditModal();
            }
        });
        
        // Form submission
        const form = profileEditModal.querySelector('#profileEditForm');
        if (form) {
            form.addEventListener('submit', handleProfileEditSubmit);
        }
    }
    
    // FAQ Modal
    const faqModal = document.getElementById('faqModal');
    if (faqModal) {
        // Close button
        const closeBtn = faqModal.querySelector('.modal-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', closeFAQModal);
        }
        
        // Click outside to close
        faqModal.addEventListener('click', (event) => {
            if (event.target === faqModal) {
                closeFAQModal();
            }
        });
    }
    
    // Global escape key handler for all modals
    document.addEventListener('keydown', (event) => {
        if (event.key === 'Escape') {
            // Close any open modal
            const openModals = document.querySelectorAll('.modal[style*="display: flex"]');
            openModals.forEach(modal => {
                if (modal.id === 'deleteAgentModal') hideDeleteAgentModal();
                else if (modal.id === 'profileEditModal') hideProfileEditModal();
                else if (modal.id === 'faqModal') closeFAQModal();
            });
        }
    });
}

// Initialize modals when DOM is ready
function initializeModals() {
    setupModalEventListeners();
}

// Make functions globally available
window.showDeleteAgentModal = showDeleteAgentModal;
window.hideDeleteAgentModal = hideDeleteAgentModal;
window.showProfileEditModal = showProfileEditModal;
window.hideProfileEditModal = hideProfileEditModal;
window.handleProfileEditSubmit = handleProfileEditSubmit;
window.openFAQModal = openFAQModal;
window.closeFAQModal = closeFAQModal;
window.setupModalEventListeners = setupModalEventListeners;
window.initializeModals = initializeModals;
